package dev.pigmomo.yhkit2025.ui.dialog.productmonitor

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.pigmomo.yhkit2025.data.model.*
import dev.pigmomo.yhkit2025.api.model.user.AddressItem
import dev.pigmomo.yhkit2025.api.model.cart.CartItem
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringOperationType
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringPlanEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringType
import dev.pigmomo.yhkit2025.service.productmonitor.MonitoringServiceManager
import dev.pigmomo.yhkit2025.ui.theme.dialogContainerColor
import kotlinx.coroutines.launch

/**
 * 监控任务弹窗（支持新增和编辑）
 * @param onDismiss 关闭弹窗回调
 * @param editingPlan 要编辑的监控计划（为null时表示新增模式）
 * @param currentAccount 当前选中的账号（仅新增模式需要）
 * @param serviceType 服务类型（仅新增模式需要）
 * @param selectedAddressItem 选中的地址（仅新增模式需要）
 * @param currentCartItem 当前购物车项（仅新增模式需要）
 * @param selectedDistrict 选中的地区（仅新增模式需要）
 * @param xyhBizParams XYH业务参数（仅新增模式需要）
 * @param webXyhBizParams Web XYH业务参数（仅新增模式需要）
 * @param shopId 店铺ID（仅新增模式需要）
 * @param sellerId 卖家ID（仅新增模式需要）
 * @param cityId 城市ID（仅新增模式需要）
 * @param onTaskCreated 任务创建/更新成功回调
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddMonitoringTaskDialog(
    onDismiss: () -> Unit,
    editingPlan: MonitoringPlanEntity? = null,
    currentAccount: OrderTokenEntity? = null,
    serviceType: String = "",
    selectedAddressItem: AddressItem? = null,
    currentCartItem: CartItem? = null,
    selectedDistrict: String = "",
    xyhBizParams: String = "",
    webXyhBizParams: String = "",
    shopId: String = "",
    sellerId: String = "",
    cityId: String = "",
    onTaskCreated: () -> Unit
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()

    // 判断是否为编辑模式
    val isEditMode = editingPlan != null

    // 状态管理 - 根据模式初始化
    var taskName by remember { mutableStateOf(editingPlan?.name ?: "") }
    var selectedType by remember { mutableStateOf(editingPlan?.type ?: MonitoringType.CART) }
    var selectedOperationType by remember { mutableStateOf(editingPlan?.operationType ?: MonitoringOperationType.INTERVAL) }
    var intervalSeconds by remember { mutableStateOf(editingPlan?.intervalSeconds?.toString() ?: "60") }
    var scheduledConfig by remember { mutableStateOf(editingPlan?.scheduledConfig ?: """{"hours":[9,12,18],"minutes":[0,30]}""") }
    var productIds by remember { mutableStateOf(editingPlan?.productIds?.takeIf { it.isNotEmpty() } ?: listOf("")) }
    var priority by remember { mutableStateOf(editingPlan?.priority?.toString() ?: "5") }
    var maxExecutions by remember { mutableStateOf(editingPlan?.maxExecutions?.toString() ?: "-1") }
    var maxRetries by remember { mutableStateOf(editingPlan?.maxRetries?.toString() ?: "3") }
    var retryIntervalSeconds by remember { mutableStateOf(editingPlan?.retryIntervalSeconds?.toString() ?: "30") }
    var enableRetry by remember { mutableStateOf(editingPlan?.enableRetry ?: true) }
    var note by remember { mutableStateOf(editingPlan?.note ?: "") }
    var isEnabled by remember { mutableStateOf(editingPlan?.isEnabled ?: true) }
    var isCreating by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf("") }

    // 检查是否有可用账号（仅新增模式需要）
    val hasValidAccount = if (isEditMode) true else (currentAccount != null && currentAccount.isLogin)

    AlertDialog(
        onDismissRequest = onDismiss,
        confirmButton = {
            Button(
                onClick = {
                    if (!hasValidAccount) {
                        errorMessage = "请先载入有效账号"
                        return@Button
                    }

                    if (taskName.isBlank()) {
                        errorMessage = "请输入任务名称"
                        return@Button
                    }

                    if (productIds.all { it.isBlank() } && selectedType == MonitoringType.PRODUCT_DETAIL) {
                        errorMessage = "商品详情监控需要至少一个商品ID"
                        return@Button
                    }

                    scope.launch {
                        isCreating = true
                        try {
                            val monitoringPlanRepository = MonitoringServiceManager.getMonitoringPlanRepository(context)

                            if (isEditMode && editingPlan != null) {
                                // 编辑模式 - 更新现有任务
                                val updatedPlan = editingPlan.copy(
                                    name = taskName,
                                    type = selectedType,
                                    productIds = productIds.filter { it.isNotBlank() },
                                    operationType = selectedOperationType,
                                    intervalSeconds = intervalSeconds.toIntOrNull() ?: 60,
                                    scheduledConfig = scheduledConfig,
                                    priority = priority.toIntOrNull() ?: 5,
                                    maxExecutions = maxExecutions.toIntOrNull() ?: -1,
                                    maxRetries = maxRetries.toIntOrNull() ?: 3,
                                    retryIntervalSeconds = retryIntervalSeconds.toIntOrNull() ?: 30,
                                    enableRetry = enableRetry,
                                    note = note,
                                    isEnabled = isEnabled
                                )

                                val success = monitoringPlanRepository.updateMonitoringPlan(updatedPlan)
                                if (success) {
                                    onTaskCreated()
                                    onDismiss()
                                } else {
                                    errorMessage = "更新任务失败"
                                }
                            } else {
                                // 新增模式 - 创建新任务
                                val plan = MonitoringPlanEntity(
                                    name = taskName,
                                    type = selectedType,
                                    account = currentAccount!!,
                                    productIds = productIds.filter { it.isNotBlank() },
                                    operationType = selectedOperationType,
                                    intervalSeconds = intervalSeconds.toIntOrNull() ?: 60,
                                    scheduledConfig = scheduledConfig,
                                    priority = priority.toIntOrNull() ?: 5,
                                    maxExecutions = maxExecutions.toIntOrNull() ?: -1,
                                    maxRetries = maxRetries.toIntOrNull() ?: 3,
                                    retryIntervalSeconds = retryIntervalSeconds.toIntOrNull() ?: 30,
                                    enableRetry = enableRetry,
                                    serviceType = serviceType,
                                    addressId = selectedAddressItem?.id ?: "",
                                    xyhBizParams = xyhBizParams,
                                    webXyhBizParams = webXyhBizParams,
                                    shopId = shopId,
                                    sellerId = sellerId,
                                    cityId = cityId,
                                    district = selectedDistrict,
                                    note = note,
                                    isEnabled = isEnabled
                                )

                                monitoringPlanRepository.addMonitoringPlan(plan)
                                onTaskCreated()
                            }
                        } catch (e: Exception) {
                            errorMessage = if (isEditMode) "更新任务失败: ${e.message}" else "创建任务错误: ${e.message}"
                        } finally {
                            isCreating = false
                        }
                    }
                },
                enabled = !isCreating && hasValidAccount
            ) {
                if (isCreating) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp
                    )
                } else {
                    Text(if (isEditMode) "保存" else "创建")
                }
            }
        },
        dismissButton = {
            Button(onClick = onDismiss) {
                Text("取消")
            }
        },
        title = {
            Text(if (isEditMode) "编辑监控任务" else "添加监控任务")
        },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(max = 600.dp)
                    .verticalScroll(rememberScrollState())
            ) {
                // 账号状态提示（仅新增模式）
                if (!hasValidAccount) {
                    Card(
                        colors = CardDefaults.cardColors(containerColor = Color.Red.copy(alpha = 0.1f)),
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text(
                            text = if (currentAccount == null) "未选择账号，请先选择一个账号" else "当前账号未登录，请先登录账号",
                            color = Color.Red,
                            modifier = Modifier.padding(12.dp),
                            fontSize = 14.sp
                        )
                    }
                }

                // 任务名称
                OutlinedTextField(
                    value = taskName,
                    onValueChange = {
                        taskName = it
                        errorMessage = ""
                    },
                    label = { Text("任务名称") },
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(8.dp),
                    enabled = hasValidAccount
                )

                // 编辑模式下显示启用状态
                if (isEditMode) {
                    Spacer(modifier = Modifier.height(8.dp))
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(16.dp)
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Switch(
                                checked = isEnabled,
                                onCheckedChange = { isEnabled = it }
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text("启用任务")
                        }

                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Switch(
                                checked = enableRetry,
                                onCheckedChange = { enableRetry = it }
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text("启用重试")
                        }
                    }
                }

                // 监控类型选择
                Spacer(modifier = Modifier.height(8.dp))
                Text("监控类型", fontWeight = FontWeight.Medium)
                Spacer(modifier = Modifier.height(8.dp))
                Column(modifier = Modifier.selectableGroup()) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .selectable(
                                selected = selectedType == MonitoringType.CART,
                                onClick = { selectedType = MonitoringType.CART },
                                role = Role.RadioButton
                            ),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = selectedType == MonitoringType.CART,
                            onClick = null,
                            enabled = hasValidAccount
                        )
                        Text("购物车监控")
                    }
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clip(RoundedCornerShape(8.dp))
                            .selectable(
                                selected = selectedType == MonitoringType.PRODUCT_DETAIL,
                                onClick = { selectedType = MonitoringType.PRODUCT_DETAIL },
                                role = Role.RadioButton
                            ),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = selectedType == MonitoringType.PRODUCT_DETAIL,
                            onClick = null,
                            enabled = hasValidAccount
                        )
                        Text("商品详情监控")
                    }
                }

                // 操作类型选择
                Spacer(modifier = Modifier.height(8.dp))
                Text("操作类型", fontWeight = FontWeight.Medium)
                Spacer(modifier = Modifier.height(8.dp))
                Column(modifier = Modifier.selectableGroup()) {
                    MonitoringOperationType.entries.forEach { type ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clip(RoundedCornerShape(8.dp))
                                .selectable(
                                    selected = selectedOperationType == type,
                                    onClick = { selectedOperationType = type },
                                    role = Role.RadioButton
                                ),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            RadioButton(
                                selected = selectedOperationType == type,
                                onClick = null,
                                enabled = hasValidAccount
                            )
                            Text(
                                text = when (type) {
                                    MonitoringOperationType.INTERVAL -> "间隔监控"
                                    MonitoringOperationType.SCHEDULED -> "定时监控"
                                    MonitoringOperationType.MANUAL -> "手动监控"
                                },
                            )
                        }
                    }
                }

                // 间隔时间设置（仅间隔监控显示）
                if (selectedOperationType == MonitoringOperationType.INTERVAL) {
                    Spacer(modifier = Modifier.height(8.dp))
                    OutlinedTextField(
                        value = intervalSeconds,
                        onValueChange = { intervalSeconds = it },
                        label = { Text("监控间隔（秒）") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        modifier = Modifier.fillMaxWidth(),
                        shape = RoundedCornerShape(8.dp),
                        enabled = hasValidAccount
                    )
                }

                // 定时配置（仅定时监控显示）
                if (selectedOperationType == MonitoringOperationType.SCHEDULED) {
                    Spacer(modifier = Modifier.height(8.dp))
                    OutlinedTextField(
                        value = scheduledConfig,
                        onValueChange = { scheduledConfig = it },
                        label = { Text("定时配置（JSON格式）") },
                        modifier = Modifier.fillMaxWidth(),
                        shape = RoundedCornerShape(8.dp),
                        enabled = hasValidAccount,
                        maxLines = 1
                    )
                }

                // 商品ID列表（仅商品详情监控显示）
                if (selectedType == MonitoringType.PRODUCT_DETAIL) {
                    Spacer(modifier = Modifier.height(8.dp))
                    Text("商品ID", fontWeight = FontWeight.Medium)
                    productIds.forEachIndexed { index, productId ->
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            OutlinedTextField(
                                value = productId,
                                onValueChange = { newValue ->
                                    productIds = productIds.toMutableList().apply {
                                        this[index] = newValue
                                    }
                                },
                                label = { Text("商品ID") },
                                modifier = Modifier.weight(1f),
                                shape = RoundedCornerShape(8.dp),
                                enabled = hasValidAccount
                            )
                            if (productIds.size > 1) {
                                IconButton(
                                    onClick = {
                                        productIds = productIds.toMutableList().apply {
                                            removeAt(index)
                                        }
                                    },
                                    enabled = hasValidAccount
                                ) {
                                    Icon(Icons.Default.Delete, contentDescription = "删除")
                                }
                            }
                        }
                    }
                    TextButton(
                        onClick = {
                            productIds = productIds + ""
                        },
                        enabled = hasValidAccount,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Icon(Icons.Default.Add, contentDescription = null)
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("添加商品ID")
                    }
                }

                // 高级设置
                Spacer(modifier = Modifier.height(8.dp))
                Text("高级设置", fontWeight = FontWeight.Medium)
                Spacer(modifier = Modifier.height(8.dp))
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    OutlinedTextField(
                        value = priority,
                        onValueChange = { priority = it },
                        label = { Text("优先级(1-10)") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        modifier = Modifier.weight(1f),
                        shape = RoundedCornerShape(8.dp),
                        enabled = hasValidAccount
                    )
                    OutlinedTextField(
                        value = maxExecutions,
                        onValueChange = { maxExecutions = it },
                        label = { Text("最大执行次数") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        modifier = Modifier.weight(1f),
                        shape = RoundedCornerShape(8.dp),
                        enabled = hasValidAccount
                    )
                }

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    OutlinedTextField(
                        value = maxRetries,
                        onValueChange = { maxRetries = it },
                        label = { Text("最大重试次数") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        modifier = Modifier.weight(1f),
                        shape = RoundedCornerShape(8.dp),
                        enabled = hasValidAccount
                    )
                    OutlinedTextField(
                        value = retryIntervalSeconds,
                        onValueChange = { retryIntervalSeconds = it },
                        label = { Text("重试间隔(秒)") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        modifier = Modifier.weight(1f),
                        shape = RoundedCornerShape(8.dp),
                        enabled = hasValidAccount
                    )
                }

                // 备注
                OutlinedTextField(
                    value = note,
                    onValueChange = { note = it },
                    label = { Text("备注") },
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(8.dp),
                    enabled = hasValidAccount,
                    minLines = 1
                )

                // 错误信息显示
                if (errorMessage.isNotEmpty()) {
                    Card(
                        colors = CardDefaults.cardColors(containerColor = Color.Red.copy(alpha = 0.1f)),
                        modifier = Modifier.fillMaxWidth().padding(top = 16.dp),
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        Text(
                            text = errorMessage,
                            color = Color.Red,
                            modifier = Modifier.padding(12.dp),
                            fontSize = 14.sp
                        )
                    }
                }
            }
        },
        containerColor = dialogContainerColor()
    )
}
