package dev.pigmomo.yhkit2025.ui.dialog.productmonitor

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Done
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import android.util.Log
import androidx.compose.foundation.shape.RoundedCornerShape

import dev.pigmomo.yhkit2025.R
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringPlanEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringOperationType
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringType
import dev.pigmomo.yhkit2025.service.productmonitor.MonitoringServiceManager
import dev.pigmomo.yhkit2025.service.productmonitor.MonitoringNotificationService
import dev.pigmomo.yhkit2025.service.productmonitor.MonitoringTaskResult
import dev.pigmomo.yhkit2025.service.productmonitor.MonitoringEventBus
import dev.pigmomo.yhkit2025.ui.components.TokenActionButton
import dev.pigmomo.yhkit2025.ui.theme.cardThemeOverlay
import dev.pigmomo.yhkit2025.ui.theme.dialogContainerColor
import dev.pigmomo.yhkit2025.utils.productmonitor.MonitoringStatusUtils
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*
import kotlin.math.abs

/**
 * 监控任务列表弹窗
 * @param onDismiss 关闭弹窗回调
 * @param onShowAddDialog 显示添加监控任务弹窗回调
 * @param onNavigateToDataScreen 导航到监控数据页面回调
 */
@Composable
fun MonitoringTaskDialog(
    onDismiss: () -> Unit,
    onShowAddDialog: () -> Unit,
    onNavigateToDataScreen: () -> Unit = {}
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()

    // 状态管理
    var monitoringPlans by remember { mutableStateOf<List<MonitoringPlanEntity>>(emptyList()) }
    var isLoading by remember { mutableStateOf(true) }
    var isExecuting by remember { mutableStateOf(false) }
    var executionResults by remember { mutableStateOf<Map<Int, MonitoringTaskResult>>(emptyMap()) }
    var selectedTabIndex by remember { mutableIntStateOf(0) }
    var selectedPlan by remember { mutableStateOf<MonitoringPlanEntity?>(null) }
    var showEditDialog by remember { mutableStateOf(false) }
    var editingPlan by remember { mutableStateOf<MonitoringPlanEntity?>(null) }
    var refreshTrigger by remember { mutableIntStateOf(0) } // 用于强制刷新状态

    // 获取服务实例
    val monitoringPlanRepository =
        remember { MonitoringServiceManager.getMonitoringPlanRepository(context) }
    val schedulerService = remember { MonitoringServiceManager.getSchedulerService(context) }

    // 监控调度器状态
    var isSchedulerRunning by remember { mutableStateOf(false) }
    var nextScheduleTime by remember { mutableStateOf<Date?>(null) }

    // 检查调度器状态和下次执行时间
    LaunchedEffect(Unit) {
        isSchedulerRunning = schedulerService.isSchedulerRunning()
        nextScheduleTime = schedulerService.getNextScheduleCheckTime()
    }

    // 定时更新调度器下次执行时间
    LaunchedEffect(isSchedulerRunning) {
        if (isSchedulerRunning) {
            while (true) {
                delay(1000) // 每秒更新一次
                nextScheduleTime = schedulerService.getNextScheduleCheckTime()
            }
        } else {
            nextScheduleTime = null
        }
    }

    // 刷新监控计划数据的函数
    val refreshPlansData = suspend {
        try {
            // 添加小延迟确保数据库更新完成
            delay(100)
            val updatedPlans = monitoringPlanRepository.getAllMonitoringPlans().first()
            Log.d("MonitoringTaskDialog", "刷新监控计划数据，共 ${updatedPlans.size} 个计划")
            monitoringPlans = updatedPlans
            refreshTrigger++
            Log.d("MonitoringTaskDialog", "refreshTrigger 更新为: $refreshTrigger")
        } catch (e: Exception) {
            Log.e("MonitoringTaskDialog", "刷新监控计划数据失败", e)
        }
    }

    // 监听监控事件
    LaunchedEffect(Unit) {
        MonitoringEventBus.events.collect { event ->
            when (event) {
                is MonitoringEventBus.MonitoringEvent.TaskExecuted -> {
                    // 单个任务执行完成，刷新数据并更新执行结果
                    refreshPlansData()
                    // 更新执行结果显示，保留完整的执行结果数据
                    executionResults = executionResults + (event.planId to event.result)
                }

                is MonitoringEventBus.MonitoringEvent.BatchTasksExecuted -> {
                    // 批量任务执行完成，刷新数据并更新执行结果
                    refreshPlansData()
                    // 更新批量执行结果显示，保留完整的执行结果数据
                    val newResults = event.results.toMap()
                    executionResults = executionResults + newResults
                }

                is MonitoringEventBus.MonitoringEvent.SchedulerStatusChanged -> {
                    // 调度器状态变化
                    isSchedulerRunning = event.isRunning
                    nextScheduleTime = schedulerService.getNextScheduleCheckTime()
                }

                is MonitoringEventBus.MonitoringEvent.PlansDataUpdated -> {
                    // 监控计划数据更新
                    refreshPlansData()
                }
            }
        }
    }

    // 标签页选项
    val tabs = listOf("全部", "间隔", "定时", "手动")

    // 加载监控计划
    LaunchedEffect(Unit) {
        scope.launch {
            try {
                monitoringPlanRepository.getAllMonitoringPlans().collect { plans ->
                    monitoringPlans = plans
                    isLoading = false
                }
            } catch (e: Exception) {
                isLoading = false
            }
        }
    }

    // 根据选中的标签页过滤数据
    val filteredPlans = remember(monitoringPlans, selectedTabIndex) {
        when (selectedTabIndex) {
            0 -> monitoringPlans // 全部
            1 -> monitoringPlans.filter { it.operationType == MonitoringOperationType.INTERVAL }
            2 -> monitoringPlans.filter { it.operationType == MonitoringOperationType.SCHEDULED }
            3 -> monitoringPlans.filter { it.operationType == MonitoringOperationType.MANUAL }
            else -> monitoringPlans
        }
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        confirmButton = {},
        title = {
            Column {
                Text("监控任务列表")

                Row(
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Box(
                        modifier = Modifier
                            .size(8.dp)
                            .background(
                                color = if (isSchedulerRunning) Color.Blue else Color.Red,
                                shape = CircleShape
                            )
                    )

                    Spacer(modifier = Modifier.width(6.dp))

                    val dateFormat = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
                    Text(
                        text = if (isSchedulerRunning) "调度器运行中 下次检查时间: ${
                            dateFormat.format(
                                nextScheduleTime!!
                            )
                        }" else "调度器已停止",
                        fontSize = 12.sp,
                        color = if (isSchedulerRunning) Color.Blue else Color.Red
                    )
                }
            }
        },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(max = 600.dp)
            ) {

                // 操作按钮栏
                Card(
                    colors = cardThemeOverlay(),
                    modifier = Modifier
                        .padding(top = 2.dp, bottom = 2.dp)
                        .fillMaxWidth()
                ) {
                    Row(
                        modifier = Modifier.horizontalScroll(rememberScrollState())
                    ) {
                        TokenActionButton(
                            icon = R.drawable.baseline_query_stats_24,
                            text = "监控数据",
                            onClick = {
                                onNavigateToDataScreen()
                            }
                        )

                        TokenActionButton(
                            icon = R.drawable.baseline_all_inclusive_24,
                            text = "执行全部",
                            onClick = {
                                if (!isExecuting) {
                                    scope.launch {
                                        isExecuting = true
                                        try {
                                            val results =
                                                schedulerService.forceExecuteAllEnabledPlans()
                                            executionResults =
                                                results.associate { it.first.id to it.second }

                                            // 执行完成后，刷新监控计划列表以获取最新数据
                                            val successCount = results.count { it.second.success }
                                            if (successCount > 0) {
                                                refreshPlansData()
                                            }
                                        } catch (e: Exception) {
                                            // 处理错误
                                        } finally {
                                            isExecuting = false
                                        }
                                    }
                                }
                            }
                        )

                        TokenActionButton(
                            icon = R.drawable.outline_timer_24,
                            text = "启动调度",
                            onClick = {
                                scope.launch {
                                    try {
                                        if (!schedulerService.isSchedulerRunning()) {
                                            schedulerService.startScheduler()
                                        }
                                        isSchedulerRunning = schedulerService.isSchedulerRunning()
                                    } catch (e: Exception) {
                                        // 处理启动错误
                                    }
                                }
                            }
                        )

                        TokenActionButton(
                            icon = R.drawable.outline_timer_off_24,
                            text = "停止调度",
                            onClick = {
                                scope.launch {
                                    try {
                                        if (schedulerService.isSchedulerRunning()) {
                                            schedulerService.stopScheduler()
                                        }
                                        isSchedulerRunning = schedulerService.isSchedulerRunning()
                                    } catch (e: Exception) {
                                        // 处理停止错误
                                    }
                                }
                            }
                        )

                        TokenActionButton(
                            imageVector = Icons.Default.Refresh,
                            text = "刷新",
                            onClick = {
                                scope.launch {
                                    isLoading = true
                                    try {
                                        // 使用refreshPlansData函数，确保refreshTrigger也会更新
                                        refreshPlansData()
                                        isLoading = false
                                    } catch (e: Exception) {
                                        isLoading = false
                                    }
                                }
                            }
                        )

                        TokenActionButton(
                            imageVector = Icons.Default.Add,
                            text = "添加任务",
                            onClick = {
                                onShowAddDialog()
                            }
                        )

                        // 调试按钮 - 测试通知
                        /*TokenActionButton(
                            icon = R.drawable.baseline_assistant_photo_24,
                            text = "测试通知",
                            onClick = {
                                scope.launch {
                                    try {
                                        MonitoringNotificationService.sendTestNotification(context)
                                    } catch (e: Exception) {
                                        Log.e(
                                            "MonitoringTaskDialog",
                                            "Failed to send test notification",
                                            e
                                        )
                                    }
                                }
                            }
                        )*/
                    }
                }

                // 过滤标签
                Row(
                    modifier = Modifier
                        .horizontalScroll(rememberScrollState())
                        .padding(vertical = 8.dp),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    tabs.forEachIndexed { index, title ->
                        FilterChip(
                            selected = selectedTabIndex == index,
                            onClick = { selectedTabIndex = index },
                            label = { Text(title) }
                        )
                    }
                }

                // 任务列表
                if (isLoading) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator()
                    }
                } else if (filteredPlans.isEmpty()) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "监控任务为空",
                            color = Color.Gray
                        )
                    }
                } else {
                    LazyColumn(
                        modifier = Modifier.fillMaxSize(),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(filteredPlans) { plan ->
                            MonitoringTaskItem(
                                plan = plan,
                                executionResult = executionResults[plan.id],
                                isExecuting = isExecuting,
                                refreshTrigger = refreshTrigger,
                                onExecute = { selectedPlan ->
                                    scope.launch {
                                        try {
                                            val result =
                                                schedulerService.executeManualTask(selectedPlan.id)
                                            executionResults =
                                                executionResults + (selectedPlan.id to result)

                                            // 执行完成后，刷新监控计划列表以获取最新数据
                                            if (result.success) {
                                                refreshPlansData()
                                            }
                                        } catch (e: Exception) {
                                            // 处理错误
                                        }
                                    }
                                },
                                onShowDetail = { plan ->
                                    selectedPlan = plan
                                },
                                isSchedulerRunning = isSchedulerRunning
                            )
                        }
                    }
                }
            }
        },
        containerColor = dialogContainerColor()
    )

    // 任务详情弹窗
    selectedPlan?.let { plan ->
        MonitoringTaskDetailDialog(
            plan = plan,
            onDismiss = { selectedPlan = null },
            onEdit = { editPlan ->
                selectedPlan = null
                editingPlan = editPlan
                showEditDialog = true
            },
            onDelete = { deletePlan ->
                scope.launch {
                    try {
                        monitoringPlanRepository.deleteMonitoringPlanById(deletePlan.id)
                        // 刷新列表
                        monitoringPlans = monitoringPlanRepository.getAllMonitoringPlans().first()
                    } catch (e: Exception) {
                        // 处理删除错误
                    }
                }
            },
            isSchedulerRunning = isSchedulerRunning
        )
    }

    // 编辑任务弹窗
    if (showEditDialog && editingPlan != null) {
        AddMonitoringTaskDialog(
            editingPlan = editingPlan!!,
            onDismiss = {
                showEditDialog = false
                editingPlan = null
            },
            onTaskCreated = {
                showEditDialog = false
                editingPlan = null
                // 刷新列表
                scope.launch {
                    try {
                        monitoringPlans = monitoringPlanRepository.getAllMonitoringPlans().first()
                    } catch (e: Exception) {
                        // 处理错误
                    }
                }
            }
        )
    }
}

/**
 * 监控任务项组件
 */
@Composable
fun MonitoringTaskItem(
    plan: MonitoringPlanEntity,
    executionResult: MonitoringTaskResult?,
    isExecuting: Boolean,
    refreshTrigger: Int = 0,
    onExecute: (MonitoringPlanEntity) -> Unit,
    onShowDetail: (MonitoringPlanEntity) -> Unit,
    isSchedulerRunning: Boolean
) {
    val dateFormat = remember { SimpleDateFormat("MM-dd HH:mm", Locale.getDefault()) }

    // 获取监控状态
    var monitoringStatus by remember(
        plan.lastExecutedAt,
        plan.executedCount,
        refreshTrigger,
        isSchedulerRunning
    ) {
        mutableStateOf(
            MonitoringStatusUtils.getMonitoringStatus(
                plan,
                isSchedulerRunning = isSchedulerRunning
            )
        )
    }

    // 定时更新状态（对所有任务都进行定时更新）
    LaunchedEffect(
        plan.id,
        plan.lastExecutedAt,
        plan.executedCount,
        isSchedulerRunning,
        refreshTrigger
    ) {
        while (true) {
            delay(1000) // 每秒更新一次
            val newStatus = MonitoringStatusUtils.getMonitoringStatus(
                plan,
                isSchedulerRunning = isSchedulerRunning
            )

            // 总是更新状态，确保UI实时反映最新状态
            val oldStatusText = monitoringStatus.statusText
            monitoringStatus = newStatus

            // 只在状态文本发生变化时打印日志
            if (newStatus.statusText != oldStatusText) {
                Log.d(
                    "MonitoringTaskItem",
                    "监控计划 ${plan.name} 状态变化: $oldStatusText -> ${newStatus.statusText}"
                )
            }
        }
    }

    // 监听任务执行完成事件，强制刷新状态
    LaunchedEffect(plan.id) {
        MonitoringEventBus.events.collect { event ->
            when (event) {
                is MonitoringEventBus.MonitoringEvent.TaskExecuted -> {
                    if (event.planId == plan.id) {
                        Log.d("MonitoringTaskItem", "监控计划 ${plan.name} 执行完成，强制刷新状态")
                        // 添加小延迟确保数据库更新完成
                        delay(200)
                        // 立即更新状态，不等待定时器
                        monitoringStatus = MonitoringStatusUtils.getMonitoringStatus(
                            plan,
                            isSchedulerRunning = isSchedulerRunning
                        )
                    }
                }

                is MonitoringEventBus.MonitoringEvent.PlansDataUpdated -> {
                    // 监控计划数据更新时也立即刷新状态
                    Log.d("MonitoringTaskItem", "监控计划 ${plan.name} 数据更新，刷新状态")
                    delay(100)
                    monitoringStatus = MonitoringStatusUtils.getMonitoringStatus(
                        plan,
                        isSchedulerRunning = isSchedulerRunning
                    )
                }

                else -> {}
            }
        }
    }

    // 当plan的关键字段变化时，立即更新状态
    LaunchedEffect(
        plan.lastExecutedAt,
        plan.executedCount,
        plan.isEnabled,
        refreshTrigger,
        isSchedulerRunning
    ) {
        Log.d(
            "MonitoringTaskItem",
            "监控计划 ${plan.name} 状态更新: lastExecutedAt=${plan.lastExecutedAt}, refreshTrigger=$refreshTrigger"
        )
        monitoringStatus =
            MonitoringStatusUtils.getMonitoringStatus(plan, isSchedulerRunning = isSchedulerRunning)
    }

    Card(
        modifier = Modifier
            .fillMaxWidth(),
        colors = cardThemeOverlay()
    ) {
        Column(
            modifier = Modifier
                .clickable { onShowDetail(plan) }
                .padding(12.dp)
        ) {
            // 任务标题和状态
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    // 任务名称和执行按钮在同一行
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = plan.name,
                            fontWeight = FontWeight.Bold,
                            fontSize = 16.sp,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis,
                            modifier = Modifier.weight(1f)
                        )

                        // 执行按钮
                        IconButton(
                            onClick = { onExecute(plan) },
                            enabled = plan.isEnabled && !isExecuting,
                            modifier = Modifier.size(24.dp)
                        ) {
                            Icon(
                                Icons.Default.Refresh,
                                contentDescription = "执行",
                                tint = if (plan.isEnabled) MaterialTheme.colorScheme.primary else Color.Gray
                            )
                        }
                    }

                    Row {
                        Text(
                            text = when (plan.type) {
                                MonitoringType.CART -> "购物车"
                                MonitoringType.PRODUCT_DETAIL -> "商品详情"
                            },
                            fontSize = 12.sp,
                            color = Color.Gray,
                            modifier = Modifier.padding(end = 8.dp)
                        )

                        Text(
                            text = when (plan.operationType) {
                                MonitoringOperationType.INTERVAL -> "间隔监控"
                                MonitoringOperationType.SCHEDULED -> "定时监控"
                                MonitoringOperationType.MANUAL -> "手动监控"
                            },
                            fontSize = 12.sp,
                            color = Color.Gray
                        )
                    }
                }
            }

            // 显示等待状态
            if (monitoringStatus.isWaiting || plan.operationType != MonitoringOperationType.MANUAL) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = monitoringStatus.statusText,
                        fontSize = 12.sp,
                        color = monitoringStatus.statusColor,
                        fontWeight = FontWeight.Medium
                    )

                    // 显示下次执行时间（准备执行时不显示）
                    if (monitoringStatus.nextExecutionTime != null && !monitoringStatus.statusText.contains(
                            "准备执行"
                        )
                    ) {
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "下次执行时间: ${
                                MonitoringStatusUtils.formatNextExecutionTime(
                                    monitoringStatus.nextExecutionTime
                                )
                            }",
                            fontSize = 10.sp,
                            color = Color.Gray
                        )
                    }
                }
            }

            // 任务详情
            Row {
                if (plan.type != MonitoringType.CART) {
                    Text(
                        text = "商品数量: ${plan.productIds.size}",
                        fontSize = 12.sp,
                        color = Color.Gray
                    )
                    Spacer(modifier = Modifier.width(16.dp))
                }

                Text(
                    text = "优先级: ${plan.priority}",
                    fontSize = 12.sp,
                    color = Color.Gray
                )

                Spacer(modifier = Modifier.width(16.dp))

                Text(
                    text = "执行次数: ${plan.executedCount}",
                    fontSize = 12.sp,
                    color = Color.Gray
                )
            }

            // 显示间隔信息（仅间隔监控）
            if (plan.operationType == MonitoringOperationType.INTERVAL) {
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = "监控间隔: ${plan.intervalSeconds}秒",
                    fontSize = 12.sp,
                    color = Color.Gray
                )
            }

            // 显示定时配置（仅定时监控）
            if (plan.operationType == MonitoringOperationType.SCHEDULED && plan.scheduledConfig.isNotEmpty()) {
                Text(
                    text = "定时配置: ${plan.scheduledConfig}",
                    fontSize = 12.sp,
                    color = Color.Gray,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }

            // 最后执行时间
            plan.lastExecutedAt?.let { lastExecuted ->
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = "最后执行: ${dateFormat.format(lastExecuted)}",
                    fontSize = 12.sp,
                    color = Color.Gray
                )
            }

            // 执行结果
            executionResult?.let { result ->
                Spacer(modifier = Modifier.height(8.dp))
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            color = if (result.success) Color.Blue.copy(alpha = 0.1f) else Color.Red.copy(
                                alpha = 0.1f
                            ),
                            shape = RoundedCornerShape(4.dp)
                        )
                ) {
                    Column(modifier = Modifier.padding(horizontal = 8.dp)) {
                        Text(
                            text = if (result.success) "执行成功 ${if (result.changesDetected > 0 || result.importantChanges > 0) "检测到 ${result.changesDetected} 个变化 (${result.importantChanges} 个重要)" else ""}" else "执行错误 ${result.message}",
                            fontSize = 10.sp,
                            color = if (result.success) Color.Blue else Color.Red,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
            }
        }
    }
}
